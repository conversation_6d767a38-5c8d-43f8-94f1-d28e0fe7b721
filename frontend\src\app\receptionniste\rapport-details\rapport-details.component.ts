import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule,ReactiveFormsModule } from '@angular/forms';
import { CommonModule, DatePipe } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';

import { DemandeService } from '../../receptionniste/demandes/demande.service';
import { ActivatedRoute } from '@angular/router';
import { RapportsService } from '../rapports/rapports.service';
import { Demande } from '../demandes/demande.model';

// Font Awesome imports
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faPrint, faPen, faCheck, faPaperPlane, faEye, faDownload, faTimes, faSync } from '@fortawesome/free-solid-svg-icons';

// Excel parsing library
import * as XLSX from 'xlsx';

interface LabInfo {
  nom: string;
  adresse: string;
  contact: string;
}
// Interface for an individual analysis
interface Analysis {
  id: number;
  code_echantillon: string;
  parametre: string;
  mesurande: string | null;
  unite: string | null;
  limite_acceptabilite: string | null;
  methode_analyse_utilisee: string;
  date_analyse: string | null;
  sample: {
    id: number;
    nature_echantillon: string;
    provenance: string;
    masse_echantillon: number;
    etat: string;
    lot: string;
    nom_preleveur: string;
    reference: string;
    date_prelevement: string;
    origine_prelevement: string;
    site: string;
  };
}

// Interface for the result data
interface ResultData {
  id: number;
  demande_id: number;
  results_file: string;
  created_at: string;
  updated_at: string;
}

// Interface for the report data
interface ReportData {
  rapport_id: number;
  demande_id: number;
  creation_date: string;
  status: string;
  validation: number;
  demande_numero: string;
  notes: string | null;
  analyses: Analysis[];
}
@Component({
  selector: 'app-rapport-danalyse',
  standalone: true,
  imports: [FormsModule, CommonModule, DatePipe, ReactiveFormsModule, FontAwesomeModule],
  templateUrl: './rapport-details.component.html',
  styleUrls: ['./rapport-details.component.css']
})
export class RapportDanalyseDetailsComponent implements OnInit {
  // Font Awesome icons
  faPrint = faPrint;
  faPen = faPen;
  faCheck = faCheck;
  faPaperPlane = faPaperPlane;
  faEye = faEye;
  faDownload = faDownload;
  faTimes = faTimes;
  faSync = faSync;

  reportData: any = null;
  clientInfo: any = [];
  samples: any[] = [];
  requestedAnalyses: any[] = [];
  analysisResults: any[] = [];
  hasAccreditedAnalysis: boolean = false;
  hideButtons: boolean = false;
  userCache: { [key: number]: any } = {}; // Stores full user data
  demandeSamples: any[] = [];
  editingIndex: number | null = null;
  areFieldsEmpty: boolean = true; // Track if report fields are empty
  uniqueParameters: string[] = []; // ✅ Unique parameters from analyses
  standardMethodAnalyses: any[] = []; // ✅ Analyses with "Standard Method"
  delaiSouhaite: string | null = null; // ✅ To store delai_souhaite from demande
  analyses_accredite: number | null = null; // ✅ To store delai_souhaite from demande
  labInfo: LabInfo = {
    nom: 'Biotechnologie Bleue et Bioproduits Aquatiques - B³Aqua',
    adresse: 'INSTIM Port de Pêche La Goulette',
    contact: '+216 71 735 848'
  };
  isEditing: { [key: number]: boolean } = {}; // ✅ Track edit state per row
  analysisForm!: FormGroup; // ✅ Reactive form
  reportid: number | null = null;
  ficheTransmissionId: number | null = null;

  // Results related properties
  resultData: ResultData | null = null;
  isLoadingResults: boolean = false;
  showExcelViewer: boolean = false;
  currentFileName: string = '';
  isLoadingExcel: boolean = false;
  excelError: string | null = null;
  excelData: any[][] = [];
  excelHeaders: string[] = [];
  currentResultFile: string | null = null;
  // Variables for editable PDF
isEditingPdf: boolean = false;
editablePrintWindow: Window | null = null;
  constructor(
    public readonly dialog: MatDialog,
    private readonly fb: FormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly reportService: RapportsService,
    private readonly demandeService: DemandeService,
    private readonly route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.reportid = params['id'];
      if (this.reportid) {
        this.loadRapportDetails(this.reportid);
      }
    }); // Load a report when the component initializes

  }
  getProposedMethod(param: string): string {
    const analysis = this.analysisResults.find(a => a.parametre === param);
    return analysis ? analysis.methode_analyse_utilisee : '-';
  }
  get maxRows(): number {
    return Math.max(this.uniqueParameters.length, this.standardMethodAnalyses.length || 1);
  }
  /**
   * Fetches the report details and then retrieves the associated user details.
   */
  printRapport(): void {
    if (!this.reportData) {
      console.error('No report data available to print.');
      return;
    }

    const formattedDate = this.formatDate(this.reportData.creation_date);
    const rapportId = this.reportData.rapport_id;

    const printHtml = `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <title>Rapport d'Analyse n° ${rapportId}</title>
        <style>
          /* Existing Styles */
          @media print {
            @page { size: A4; margin: 5mm; }
            body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            .editable-field {
              border: none !important;
              background-color: transparent !important;
            }
            .print-controls {
              display: none !important;
            }
          }
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #000;
          }
          .container {
            padding: 10px;
            width: 100%;
          }
          .government-header {
            text-align: center;
            font-size: 11pt;
            margin-top: 10px;
            line-height: 1.4;
          }
          .government-header p {
            margin: 0;
          }
          .header-table {
            width: 100%;
            border-collapse: collapse;
            border-top: 4px double black;
            border-bottom: 4px double black;
            margin: 15px 0;
          }
          .header-table td {
            vertical-align: middle;
            text-align: center;
            border: none;
            padding: 5px;
          }
          .logo-left img {
            height: 90px;
            width: auto;
          }
          .center-title {
            font-weight: bold;
            font-size: 16pt;
            text-transform: uppercase;
          }
          .cartouche {
            text-align: left;
            font-size: 10pt;
            line-height: 1.6;
            flex: 1;
            white-space: nowrap;
            min-width: 180px;
          }
          .cartouche strong {
            font-weight: bold;
          }
          .validation {
            display: inline-block;
            margin-top: 5px;
            padding: 3px 10px;
            font-size: 11pt;
            border-radius: 8px;
            text-transform: uppercase;
          }
          .approved {
            color: #4caf50;
            border: 1px solid #4caf50;
            background-color: rgba(76,175,80,0.1);
          }
          .rejected {
            color: #f44336;
            border: 1px solid #f44336;
            background-color: rgba(244,67,54,0.1);
          }
          .section-title {
            margin: 18px 20px 6px;
            font-size: 12pt;
            font-weight: bold;
            text-decoration: underline;
          }
          table:not(.header-table) {
            width: calc(100% - 40px);
            margin: 0 20px 16px;
            border-collapse: collapse;
            font-size: 10pt;
          }
          table:not(.header-table) th, table:not(.header-table) td {
            border: 1px solid #000;
            padding: 4px 6px;
          }
          table:not(.header-table) th {
            background: #f2f2f2;
            font-weight: bold;
            text-align: center;
          }
          .double-border {
            border: 2px solid #000 !important;
          }
          .footer {
            margin: 20px;
            text-align: center;
            font-size: 10pt;
          }
          .footer p { margin: 4px 0; }

          /* Echantillon table styles */
          .echantillon-table {
            width: 100%;
            border-collapse: collapse;
          }

          /* Column width adjustments */
          .code-echantillon-col {
            width: 45%;
          }

          .date-reception-col {
            width: 12%;
          }

          .nature-col {
            width: 12%;
          }

          .provenance-col {
            width: 13%;
          }

          .lot-col, .reference-col {
            width: 9%;
          }

          /* Nested info styles */
          .nested-info {
            margin-top: 5px;
            font-size: 9pt;
            text-align: left;
          }

          .nested-info div {
            margin-bottom: 3px;
          }

          .nested-info strong {
            font-weight: bold;
            margin-right: 3px;
          }

          /* Editable Fields Styles */
          .editable-field {
            min-height: 20px;
            border: 1px dashed #ccc;
            padding: 2px 5px;
            background-color: #f9f9f9;
            display: inline-block;
          }
          .editable-field:focus {
            outline: none;
            border: 1px dashed #2496d3;
            background-color: #e9f7fe;
          }

          /* Print Controls Styles */
          .print-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background-color: #2496d3;
            color: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            cursor: move;
            user-select: none;
          }
          .print-controls .drag-handle {
            padding: 5px 0;
            margin-bottom: 10px;
            text-align: center;
            font-weight: bold;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            cursor: move;
          }
          .print-controls button {
            background-color: white;
            color: #2496d3;
            border: none;
            padding: 8px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            display: block;
            width: 100%;
          }
          .print-controls button:hover {
            background-color: #f0f0f0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <!-- Entête Gouvernementale -->
          <div class="government-header">
            <p><strong>République Tunisienne</strong></p>
            <p><strong>Ministère de l’Agriculture et des Ressources Hydrauliques</strong></p>
            <p><em>Institution de la Recherche et l’Enseignement Supérieur Agricoles</em></p>
            <p><em>Institut National des Sciences et Technologies de la Mer</em></p>
          </div>

          <!-- En-tête tableau (logos + titre + cartouche) -->
          <table class="header-table">
            <tr>
              <td class="logo-left" style="width: 20%;">
                <img src="${window.location.origin}/kls.png" alt="Logo INSTM" />
              </td>
              <td class="center-title" style="width: 50%;">
                <div>RAPPORT</div>
                <div>D’ANALYSE</div>
              </td>
              <td class="logo-right" style="width: 30%;">
                <div style="display: flex; align-items: center;">
                  <div style="margin-right: 10px;">
                    ${
                      this.analyses_accredite === 1
                        ? `<img src="${window.location.origin}/Image2.png" alt="Logo Tunac" style="height: 90px; width: auto;" />`
                        : `<div style="width: 90px; height: 90px;"></div>`
                    }
                  </div>
                  <div class="cartouche">
                    <strong>CODE :</strong> PE/01-PET/09<br>
                    <strong>VERSION :</strong> 07<br>
                    <strong>DATE :</strong> ${formattedDate}<br>
                    <strong>PAGE :</strong> 1/1
                  </div>
                </div>
              </td>
            </tr>
          </table>
          <h4 style="text-align: center; font-size: 1.25rem; margin: 15px 0;">N°: ${this.formatRapportId(this.reportData.rapport_id)}</h4>

          <!-- Section Client & Laboratoire -->
          <p class="section-title">Client & Laboratoire</p>
          <table class="double-border">
            <tr>
              <td style="width:50%;">
                <strong>Client</strong><br>
                Nom et Prénom : <div contenteditable="true" class="editable-field">${this.clientInfo.name || '-'} ${this.clientInfo.nickname || ''}</div><br>
                Adresse : <div contenteditable="true" class="editable-field">${this.clientInfo.adress || '-'}</div><br>
                Téléphone : <div contenteditable="true" class="editable-field">${this.clientInfo.phone || '-'}</div>
              </td>
              <td style="width:50%;">
                <strong>Laboratoire d'Analyse</strong><br>
                ${this.labInfo.nom}<br>
                ${this.labInfo.adresse}<br>
                ${this.labInfo.contact}
              </td>
            </tr>
          </table>

          <!-- Description de l’échantillon -->
          <p class="section-title">Description de l'échantillon</p>
          <table class="double-border echantillon-table">
            <thead>
              <tr>
                <th class="code-echantillon-col">Code échantillon</th>
                <th class="date-reception-col">Date réception</th>
                <th class="nature-col">Nature</th>
                <th class="provenance-col">Provenance</th>
                <th class="lot-col">Lot</th>
                <th class="reference-col">Référence</th>
              </tr>
            </thead>
            <tbody>
              ${this.samples.map(s => `
                <tr>
                  <td class="code-echantillon-col">
                    <div style="text-align: center; font-weight: bold; font-size: 12pt; margin-bottom: 5px;">${s.identification_echantillon}</div>
                    <div class="nested-info" style="margin-top: 5px; font-size: 9pt; text-align: left;">
                      <div style="white-space: nowrap; margin-bottom: 3px;"><strong>Origine prélèvement:</strong> ${s.origine_prelevement}</div>
                      <div style="margin-bottom: 3px;"><strong>Date prélèvement:</strong> ${this.formatDate(s.date_prelevement)}</div>
                      <div style="margin-bottom: 3px;"><strong>Site:</strong> ${s.site}</div>
                      <div style="margin-bottom: 3px;"><strong>Préleveur:</strong> ${s.nom_preleveur}</div>
                    </div>
                  </td>
                  <td class="date-reception-col">${this.formatDate(s.reception_date)}</td>
                  <td class="nature-col">${s.nature_echantillon}</td>
                  <td class="provenance-col">${s.provenance}</td>
                  <td class="lot-col">${s.lot}</td>
                  <td class="reference-col">${s.reference}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <!-- Analyses demandées -->
          <p class="section-title">Analyses demandées</p>
          <table class="double-border">
            <thead>
              <tr><th>N°</th><th>Paramètre</th><th>Méthode proposée</th><th>Délai d'exécution souhaité</th></tr>
            </thead>
            <tbody>
              ${this.uniqueParameters.length
                ? this.uniqueParameters.map((param, i) => `
                  <tr>
                    <td>${('0'+(i+1)).slice(-2)}</td>
                    <td>${param}</td>
                    <td>${this.standardMethodAnalyses[i]?.parametre || '-'}</td>
                    ${i === 0 ? `<td rowspan="${this.uniqueParameters.length}" style="text-align:center;">${this.delaiSouhaite || '-'}</td>` : ''}
                  </tr>
                `).join('')
                : `<tr><td>01</td><td>-</td><td>-</td><td style="text-align:center">${this.delaiSouhaite||'-'}</td></tr>`
              }
            </tbody>
          </table>

          <!-- Résultats d’analyses -->
          <p class="section-title">Résultats d'analyses</p>
          <table class="double-border">
            <thead>
              <tr>
                <th>Code échantillon</th><th>Paramètre</th><th>Mesurande</th><th>Unité</th>
                <th>Limite d'acceptabilité</th><th>Méthode utilisée</th><th>Date analyse</th>
              </tr>
            </thead>
            <tbody>
              ${this.analysisResults.map(r => `
                <tr>
                  <td>${r.code_echantillon}</td><td>${r.parametre}</td>
                  <td>${r.mesurande||'-'}</td><td>${r.unite||'-'}</td>
                  <td>${r.limite_acceptabilite||'-'}</td>
                  <td>${r.methode_analyse_utilisee}</td>
                  <td>${r.date_analyse ? this.formatDate(r.date_analyse) : '-'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <!-- Pied de page -->
          <div class="footer">
            <p>(*) Valeur inférieure à la limite de quantification</p>
            <p>Date : ${formattedDate}</p>
            <p style="font-weight:bold; margin-top:30px;">Responsable du Laboratoire</p>
            <p style="font-size:9pt;">Fin de rapport</p>
          </div>
        </div>

        <!-- Print Controls (Draggable) -->
        <div class="print-controls" id="draggable-print-controls">
          <div class="drag-handle">⋮⋮ Déplacer</div>
          <button id="print-button">Imprimer</button>
          <button id="cancel-button">Annuler</button>
        </div>

        <script>
          // Make print controls draggable
          (function() {
            const dragElement = document.getElementById('draggable-print-controls');
            if (!dragElement) return;

            let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
            const dragHandle = document.querySelector('.drag-handle');

            if (dragHandle) {
              dragHandle.onmousedown = dragMouseDown;
            } else {
              dragElement.onmousedown = dragMouseDown;
            }

            function dragMouseDown(e) {
              e.preventDefault();
              pos3 = e.clientX;
              pos4 = e.clientY;
              document.onmouseup = closeDragElement;
              document.onmousemove = elementDrag;
            }

            function elementDrag(e) {
              e.preventDefault();
              pos1 = pos3 - e.clientX;
              pos2 = pos4 - e.clientY;
              pos3 = e.clientX;
              pos4 = e.clientY;
              dragElement.style.top = (dragElement.offsetTop - pos2) + 'px';
              dragElement.style.left = (dragElement.offsetLeft - pos1) + 'px';
              dragElement.style.right = 'auto';
              dragElement.style.bottom = 'auto';
            }

            function closeDragElement() {
              document.onmouseup = null;
              document.onmousemove = null;
            }
          })();

          // Add event listeners to buttons
          document.getElementById('print-button').addEventListener('click', function() {
            document.querySelector('.print-controls').style.display = 'none';
            setTimeout(function() {
              window.print();
              document.querySelector('.print-controls').style.display = 'block';
            }, 100);
          });

          document.getElementById('cancel-button').addEventListener('click', function() {
            window.close();
          });
        </script>
      </body>
      </html>
    `;

    // Open a new window for editing and printing
    this.editablePrintWindow = window.open('', '_blank', 'width=800,height=600');
    if (this.editablePrintWindow) {
      this.editablePrintWindow.document.write(printHtml);
      this.editablePrintWindow.document.close();
      this.isEditingPdf = true; // Note: Consider renaming to isEditingHtml if more appropriate
    } else {
      console.error('Failed to open print window. Please allow pop-ups.');
    }
  }
  formatRapportId(id: number): string {
    // Pad the ID with leading zeros to make it 4 digits
    const paddedId = id.toString().padStart(4, '0');

    // Get the last two digits of the current year
    const currentYear = new Date().getFullYear();
    const yearSuffix = (currentYear % 100).toString();

    // Return the formatted ID
    return `${paddedId}/${yearSuffix}`;
  }
  formatDate(date: string): string {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`;
  }
  loadRapportDetails(rapportId: number) {
    this.reportService.getRapportDetails(rapportId).subscribe({
      next: (data: ReportData) => {
        this.reportData = data;
        console.log('rapport rapport rapport ',this.reportData);
        this.samples = this.extractSamples(data.analyses);
        this.analysisResults = data.analyses;
        this.requestedAnalyses = this.groupAnalysesBySample(data.analyses);

        // ✅ Filter analyses with "Méthode standard" first
        this.standardMethodAnalyses = data.analyses.filter(a => a.methode_analyse_utilisee === "Méthode standard");

        // ✅ Extract unique parameters, excluding those with "Méthode standard"
        this.uniqueParameters = [...new Set(
          data.analyses
            .filter(a => a.methode_analyse_utilisee !== "Méthode standard")
            .map(a => a.parametre)
        )];

        this.hasAccreditedAnalysis = this.analysisResults.some(r => r.isAccredited);
        this.initializeForm();
        if (this.reportData.demande_id) {
          this.fetchDemandeUserDetails(this.reportData.demande_id);
          this.loadResultsData(this.reportData.demande_id);
        } else {
          console.warn('No demande_id found in report.');
        }
      },
      error: () => {
        console.error(`Error fetching report details for ID ${rapportId}`);
      }
    });
  }

  /**
   * Load results data for the demande
   */
  loadResultsData(demande_numero: string) {
    this.isLoadingResults = true;
    this.reportService.getResultsAnalyseByDemandeId(demande_numero).subscribe({
      next: (response) => {
        console.log('Results response:', response);
        if (response && response.status === 'success' && response.data) {
          this.resultData = response.data;
          this.currentResultFile = response.data.results_file;
        } else {
          this.resultData = null;
        }
        this.isLoadingResults = false;
      },
      error: (error) => {
        console.error('Error loading results data:', error);
        // Check if it's a 404 with "No results found" message
        if (error.status === 404 && error.error?.message === 'No results found for this demande') {
          console.log('No results found for this demande - this is expected if no results have been uploaded yet');
        }
        this.resultData = null;
        this.isLoadingResults = false;
      }
    });
  }

  /**
   * View the result file
   */
  viewResultFile() {
    if (!this.resultData) return;

    this.showExcelViewer = true;
    this.isLoadingExcel = true;
    this.excelError = null;
    this.currentFileName = `Résultats - Demande ${this.reportData.demande_numero}`;

    this.reportService.downloadResults(this.resultData.id).subscribe({
      next: (blob) => {
        this.parseExcelFile(blob);
        console.log('results',this.resultData);
      },
      error: (error) => {
        console.error('Error downloading result file:', error);
        this.excelError = 'Impossible de charger le fichier Excel. Veuillez réessayer.';
        this.isLoadingExcel = false;
      }
    });
  }

  /**
   * Parse Excel file
   */
  parseExcelFile(blob: Blob): void {
    this.isLoadingExcel = true;
    this.excelError = null;
    this.excelHeaders = [];
    this.excelData = [];

    const reader = new FileReader();

    reader.onload = (e: ProgressEvent<FileReader>) => {
      try {
        if (!e.target?.result) {
          throw new Error('Aucun contenu lu depuis le fichier.');
        }

        // Read the file as an ArrayBuffer
        const data = new Uint8Array(e.target.result as ArrayBuffer);

        // Parse the workbook with explicit options
        const workbook = XLSX.read(data, {
          type: 'array',
          cellDates: true, // Parse dates as JavaScript Date objects
          codepage: 65001, // UTF-8 encoding
        });

        // Check if the workbook has sheets
        if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
          throw new Error('Aucune feuille trouvée dans le fichier Excel.');
        }

        // Get the first sheet
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Check if the worksheet is valid
        if (!worksheet || !worksheet['!ref']) {
          throw new Error('La feuille sélectionnée est vide ou invalide.');
        }

        // Convert to JSON with raw: false to get formatted values
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          raw: false, // Use formatted strings instead of raw values
          dateNF: 'yyyy-mm-dd', // Standardize date format
        }) as any[][];

        // Extract headers and data
        if (jsonData.length > 0) {
          // Format headers as strings, handle null/undefined
          this.excelHeaders = (jsonData[0] || []).map((header, index) =>
            header != null ? String(header) : `Colonne ${index + 1}`
          );

          // Format data rows, handle null/undefined and complex types
          this.excelData = jsonData.slice(1).map(row =>
            row.map(cell => {
              if (cell == null) return '';
              if (cell instanceof Date) return cell.toLocaleDateString('fr-FR');
              if (typeof cell === 'object') return JSON.stringify(cell);
              return String(cell).trim();
            })
          );
        } else {
          this.excelError = 'Aucune donnée trouvée dans le fichier Excel.';
        }

        this.isLoadingExcel = false;
      } catch (error) {
        console.error('Erreur lors de l\'analyse du fichier Excel:', error);
        this.excelError = error instanceof Error
          ? `Erreur: ${error.message}`
          : 'Erreur inconnue lors de l\'analyse du fichier Excel.';
        this.isLoadingExcel = false;
      }
    };

    reader.onerror = () => {
      console.error('Erreur de lecture du fichier:', reader.error);
      this.excelError = 'Impossible de lire le fichier. Vérifiez qu\'il n\'est pas corrompu.';
      this.isLoadingExcel = false;
    };

    reader.readAsArrayBuffer(blob);
  }
  /**
   * Close Excel viewer
   */
  closeExcelViewer() {
    this.showExcelViewer = false;
  }

  /**
   * Retry loading Excel file
   */
  retryLoadExcel() {
    if (this.resultData) {
      this.viewResultFile();
    }
  }

  /**
   * Download the result file
   */
  downloadResultFile() {
    if (!this.resultData) return;

    this.reportService.downloadResults(this.resultData.id).subscribe({
      next: (blob) => {
        // Create a blob URL and trigger download
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Extract filename from the path or use default
        const fileName = this.resultData?.results_file ?
                         this.getFileNameFromPath(this.resultData.results_file) :
                         `resultats_${this.reportData?.demande_numero || 'unknown'}.xlsx`;

        link.download = fileName;

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the blob URL
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error downloading result file:', error);
        alert('Impossible de télécharger le fichier de résultats. Veuillez réessayer.');
      }
    });
  }

  /**
   * Download the current file being viewed
   */
  downloadCurrentFile() {
    if (this.resultData) {
      this.downloadResultFile();
    }
  }

  /**
   * Get filename from path
   */
  getFileNameFromPath(path: string): string {
    if (!path) return '';
    const parts = path.split('/');
    return parts[parts.length - 1];
  }
  initializeForm() {
    this.analysisForm = this.fb.group({});
    this.analysisResults.forEach((result, index) => {
      this.analysisForm.addControl(`mesurande_${index}`, new FormControl({ value: result.mesurande || '', disabled: true }));
      this.analysisForm.addControl(`unite_${index}`, new FormControl({ value: result.unite || '', disabled: true }));
      this.analysisForm.addControl(`limite_acceptabilite_${index}`, new FormControl({ value: result.limite_acceptabilite || '', disabled: true }));
      this.analysisForm.addControl(`date_analyse_${index}`, new FormControl({ value: result.date_analyse || '', disabled: true }));
      this.isEditing[index] = false; // Initially, all rows are not in edit mode
    });
    this.checkIfFieldsEmpty();
  }

  /**
   * Toggles edit mode for a specific row
   */
  toggleEdit(index: number): void {
    this.isEditing[index] = !this.isEditing[index];

    // Enable/disable form controls based on edit mode
    const controls = ['mesurande', 'unite', 'limite_acceptabilite', 'date_analyse'];

    controls.forEach(control => {
      const formControl = this.analysisForm.get(`${control}_${index}`);
      if (formControl) {
        if (this.isEditing[index]) {
          formControl.enable();
        } else {
          formControl.disable();
        }
      }
    });

    this.cdr.detectChanges();
  }

  /**
   * Updates a row with new values and saves to the server
   */
  updateRow(index: number): void {
    if (!this.isEditing[index]) return;

    const result = this.analysisResults[index];
    const updatedResult = {
      ...result,
      mesurande: this.analysisForm.get(`mesurande_${index}`)?.value || null,
      unite: this.analysisForm.get(`unite_${index}`)?.value || null,
      limite_acceptabilite: this.analysisForm.get(`limite_acceptabilite_${index}`)?.value || null,
      date_analyse: this.analysisForm.get(`date_analyse_${index}`)?.value || null
    };

    this.reportService.updateAnalysisResult(updatedResult.id, updatedResult).subscribe({
      next: (response) => {
        console.log('Analysis result updated successfully:', response);
        this.analysisResults[index] = { ...updatedResult };
        this.toggleEdit(index); // Exit edit mode
        this.checkIfFieldsEmpty();
      },
      error: (error) => {
        console.error('Error updating analysis result:', error);
        // Optionally show an error message to the user
      }
    });
  }

  /**
   * Checks if all required fields are filled
   */
  checkIfFieldsEmpty(): void {
    this.areFieldsEmpty = this.analysisResults.some(result => {
      return !result.mesurande || !result.unite || !result.date_analyse;
    });
  }

  /**
   * Sends the report to the director
   */
  envoyerRapport(): void {
    if (this.areFieldsEmpty) {
      alert('Veuillez remplir tous les champs obligatoires avant d\'envoyer le rapport.');
      return;
    }

    if (!this.reportid) return;

    const isResend = this.reportData.validation === 0;
    const successMessage = isResend
      ? 'Le rapport a été renvoyé avec succès au directeur pour validation.'
      : 'Le rapport a été envoyé avec succès au directeur pour validation.';

    this.reportService.sendRapport(this.reportid).subscribe({
      next: (response) => {
        console.log('Rapport envoyé avec succès:', response);
        // Refresh the report data to get updated status and clear rejection notes
        this.loadReportData();
        alert(successMessage);
      },
      error: (error) => {
        console.error('Erreur lors de l\'envoi du rapport:', error);
        alert('Une erreur est survenue lors de l\'envoi du rapport.');
      }
    });
  }



  /**
   * Fetches the `demande` details using `demande_id` to retrieve `user_id`, then fetches user details.
   */
  fetchDemandeUserDetails(demande_id: number): void {
    if (!demande_id) {
      this.clientInfo = { name: '⏳ Chargement...', email: '', phone: '', address: '' };
      return;
    }

    this.demandeService.getDemandeBy(demande_id).subscribe({
      next: (response:Demande) => {
        const demande = response;
        console.log('demande response:', response);
        if (demande?.user_id) {
          this.fetchUserDetails(demande.user_id);
        } else {
          console.warn(`No user_id found in demande ${demande_id}`);
          this.clientInfo = { name: 'Inconnu', email: '', phone: '', address: '' };
        }
        this.delaiSouhaite = demande.delai_souhaite ?? null;
        this.analyses_accredite=demande.analyses_accredite ?? null;
        console.log('accred',this.analyses_accredite);
        console.log('delai',this.delaiSouhaite);
      },
      error: () => {
        console.error(`Error fetching demande ${demande_id}`);
        this.clientInfo = { name: 'Inconnu', email: '', phone: '', address: '' };
      }
    });
  }

  /**
   * Fetches user details using `user_id`, caches the data, and updates `clientInfo`.
   */
  fetchUserDetails(user_id: number): void {


    this.demandeService.getUserDetails(user_id).subscribe({
      next: (response) => {
        this.clientInfo=response;
        console.log('Client info:', this.clientInfo);
          this.cdr.detectChanges(); // ✅ Force Angular to detect changes

      },
      error: () => {
        console.error(`Error fetching user ${user_id}`);

      }
    });
  }


  /**
   * Groups analyses by sample code.
   */
  private groupAnalysesBySample(analyses: any[]) {
    const grouped: { [key: string]: any[] } = {};
    analyses.forEach((analysis) => {
      if (!grouped[analysis.code_echantillon]) {
        grouped[analysis.code_echantillon] = [];
      }
      grouped[analysis.code_echantillon].push(analysis);
    });

    return Object.entries(grouped).map(([key, value]) => ({
      code_echantillon: key,
      analyses: value
    }));
  }

  /**
   * Extracts unique samples from the analyses.
   */
  private extractSamples(analyses: any[]) {
    const uniqueSamples: { [key: string]: any } = {};
    analyses.forEach((analysis) => {
      const code = analysis.code_echantillon;
      if (!uniqueSamples[code] && analysis.sample) {
        uniqueSamples[code] = {
          identification_echantillon: code,
          reception_date: this.reportData?.creation_date, // Using report creation_date as a proxy; adjust if a specific sample reception date exists
          nature_echantillon: analysis.sample.nature_echantillon,
          provenance: analysis.sample.provenance,
          origine_prelevement: analysis.sample.origine_prelevement,
          date_prelevement: analysis.sample.date_prelevement,
          site: analysis.sample.site,
          lot: analysis.sample.lot,
          nom_preleveur: analysis.sample.nom_preleveur,
          reference: analysis.sample.reference,
        };
      }
    });
    return Object.values(uniqueSamples);
  }

  /**
   * Opens the accreditation confirmation modal.
   */


}
