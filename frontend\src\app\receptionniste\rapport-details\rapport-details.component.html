<div class="rapport-container" *ngIf="reportData">
    <!-- Header -->
    <header class="rapport-header">
      <div class="header-top">

        <div class="status-stamp" *ngIf="reportData.validation !== 2" [ngClass]="{
          'approved-stamp': reportData.validation === 1,
          'rejected-stamp': reportData.validation === 0
        }">
        {{ reportData.validation === 1 ? 'Approuvé' : 'Rejeté' }}

        </div>

        <div class="lab-info">
          <h2>RAPPORT D'ANALYSE N° {{ reportData.rapport_id }}</h2>
          <p>Laboratoire de gestion des analyses d'échantillons de produits aquatiques</p>
        </div>


      </div>
    </header>

    <!-- Rejection Notes Section -->
    <section class="rejection-notes-section" *ngIf="reportData.validation === 0 && reportData.notes">
      <div class="rejection-notes-container">
        <h3 class="rejection-notes-title">
          <fa-icon [icon]="faTimes" class="rejection-icon"></fa-icon>
          Motif du rejet
        </h3>
        <div class="rejection-notes-content">
          <p>{{ reportData.notes }}</p>
        </div>
      </div>
    </section>

    <!-- Infos Client + Laboratoire -->
    <section class="rapport-additional-info">
      <div class="info-left client-info">
        <h3>Client</h3>
        <table>
          <tbody>
            <tr>
              <td class="label"><strong>Nom et Prénom :</strong></td>
              <td class="input-field">{{ clientInfo.name || '⏳ Chargement...' }}   {{clientInfo.nickname }}</td>
            </tr>
            <tr>
              <td class="label"><strong>Email :</strong></td>
              <td class="input-field">{{ clientInfo.email || '--' }}</td>
            </tr>
            <tr>
              <td class="label"><strong>Téléphone :</strong></td>
              <td class="input-field">{{ clientInfo?.phone || '--' }}</td>
            </tr>
            <tr>
              <td class="label"><strong>Adresse :</strong></td>
              <td class="input-field">{{ clientInfo?.adress || '--' }}</td>
            </tr>


          </tbody>
        </table>
      </div>

      <div class="info-right laboratory-info">
        <h3>Laboratoire d'Analyse</h3>
        <table>
          <tbody>
            <tr>
              <td class="label"><strong>Nom :</strong></td>
              <td class="lab-info">{{ labInfo.nom }}</td>
            </tr>
            <tr>
              <td class="label"><strong>Adresse :</strong></td>
              <td class="lab-info">{{ labInfo.adresse }}</td>
            </tr>
            <tr>
              <td class="label"><strong>Contact :</strong></td>
              <td class="lab-info">{{ labInfo.contact }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>

    <!-- Description de l’échantillon -->
    <section class="echantillon-description-section">
      <h2>DESCRIPTION DE L'ÉCHANTILLON</h2>
      <table class="echantillon-table">
        <thead>
          <tr>
            <th class="code-echantillon-col">Code échantillon (Client)</th>
            <th class="date-reception-col">Date de réception</th>
            <th class="nature-col">Nature</th>
            <th class="provenance-col">Provenance</th>
            <th class="lot-col">Lot</th>
            <th class="reference-col">Référence</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let sample of samples">
            <td class="code-echantillon-col">
              <div style="text-align: center; font-weight: bold; font-size: 12pt; margin-bottom: 5px;">{{ sample.identification_echantillon }}</div>
              <table class="nested-table">
                <tr style="white-space: nowrap;">
                  <th>Origine du prélèvement</th><td>{{ sample.origine_prelevement }}</td>
                </tr>
                <tr>
                  <th>Date de prélèvement</th><td>{{ sample.date_prelevement | date: 'yyyy-MM-dd' }}</td>
                </tr>
                <tr>
                  <th>Site</th><td>{{ sample.site }}</td>
                </tr>
                <tr>
                  <th>Nom du préleveur</th><td>{{ sample.nom_preleveur }}</td>
                </tr>
              </table>
            </td>
            <td class="date-reception-col">{{ sample.reception_date | date: 'yyyy-MM-dd' }}</td>
            <td class="nature-col">{{ sample.nature_echantillon }}</td>
            <td class="provenance-col">{{ sample.provenance }}</td>
            <td class="lot-col">{{ sample.lot }}</td>
            <td class="reference-col">{{ sample.reference }}</td>
          </tr>
        </tbody>
      </table>
    </section>
    <!-- Description de l’échantillon -->
    <section class="rapport-description">
      <h2>Analyses Demandées</h2>
      <table>
        <thead>
          <tr>
            <th>N°</th>
            <th>Paramètre</th>
            <th>Méthode d’Analyse proposée</th>
            <th>Délai d'Exécution Souhaité</th>
          </tr>
        </thead>
        <tbody>
          <!-- Case 1: When there are unique parameters -->
          <ng-container *ngIf="uniqueParameters.length > 0">
            <tr *ngFor="let param of uniqueParameters; let i = index">
              <td>{{ ('0' + (i + 1)).slice(-2) }}</td>
              <td>{{ param }}</td>
              <td [ngStyle]="{
                'border': (standardMethodAnalyses.length === 0 && i > 0) || (standardMethodAnalyses.length > 0 && i >= standardMethodAnalyses.length) ? 'none' : '',
                'text-align': standardMethodAnalyses.length === 0 && i === 0 ? 'center' : 'center'
              }">
                <ng-container *ngIf="standardMethodAnalyses.length === 0 && i === 0">
                  Non spécifié
                </ng-container>
                <ng-container *ngIf="standardMethodAnalyses.length > 0 && i < standardMethodAnalyses.length">
                  {{ standardMethodAnalyses[i].parametre }}
                </ng-container>
              </td>
              <td *ngIf="i === 0" [attr.rowspan]="uniqueParameters.length">
                {{ delaiSouhaite || 'Non spécifié' }}
              </td>
            </tr>
          </ng-container>
          <!-- Case 2: When there are no unique parameters -->
          <ng-container *ngIf="uniqueParameters.length === 0">
            <tr>
              <td>01</td>
              <td>Non spécifié</td>
              <td [ngStyle]="{
                'border': standardMethodAnalyses.length === 0 ? 'none' : '',
                'text-align': standardMethodAnalyses.length === 0 ? 'center' : 'center'
              }">
                <ng-container *ngIf="standardMethodAnalyses.length === 0">
                  Non spécifié
                </ng-container>
                <ng-container *ngIf="standardMethodAnalyses.length > 0">
                  <ng-container *ngFor="let analysis of standardMethodAnalyses; let last = last">
                    {{ analysis.parametre }}<ng-container *ngIf="!last"><br></ng-container>
                  </ng-container>
                </ng-container>
              </td>
              <td>{{ delaiSouhaite || 'Non spécifié' }}</td>
            </tr>
          </ng-container>
        </tbody>
      </table>
    </section>
    <!-- Résultats d'analyses -->
    <section class="rapport-resultats">
      <h2>RÉSULTATS D'ANALYSES</h2>
      <form [formGroup]="analysisForm">
        <table>
          <thead>
            <tr>
              <th>Code échantillon</th>
              <th>Paramètre</th>
              <th>Mesurande</th>
              <th>Unité</th>
              <th>Limite d'acceptabilité</th>
              <th>Méthode d'Analyse utilisée</th>
              <th>Date d'Analyse</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let result of analysisResults; let i = index" [ngClass]="{'editing-row': isEditing[i]}">
              <!-- Read-only or Editable Fields -->
              <td>{{ result.code_echantillon }}</td>
              <td>{{ result.parametre }}</td>
              <td><input type="text" [formControlName]="'mesurande_' + i" placeholder="Mesurande"></td>
              <td><input type="text" [formControlName]="'unite_' + i" placeholder="Unité"></td>
              <td><input type="text" [formControlName]="'limite_acceptabilite_' + i" placeholder="Limite Acceptabilité"></td>
              <td>{{ result.methode_analyse_utilisee }}</td>
              <td><input type="date" [formControlName]="'date_analyse_' + i"></td>
              <td class="action-buttons-cell">
                <button *ngIf="!isEditing[i]" class="edit-btn" (click)="toggleEdit(i)">
                  <fa-icon [icon]="faPen" style="margin-right: 10px;"></fa-icon>Modifier
                </button>
                <button *ngIf="isEditing[i]" class="update-btn" (click)="updateRow(i)">
                  <fa-icon [icon]="faCheck" style="margin-right: 10px;"></fa-icon>Mettre à jour
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </form>
    </section>

    <!-- Résultats du fichier Excel -->
    <section class="rapport-results-file" *ngIf="reportData && reportData.demande_id">
      <h2>FICHIER DE RÉSULTATS</h2>
      <div class="results-container">
        <div *ngIf="isLoadingResults" class="loading-spinner">
          <div class="spinner"></div>
          <p>Chargement des résultats...</p>
        </div>

        <div *ngIf="!isLoadingResults && !resultData" class="no-results">
          <p>Aucun fichier de résultats n'a été téléchargé pour cette demande.</p>
        </div>

        <div *ngIf="!isLoadingResults && resultData" class="results-actions">
          <div class="result-info">
            <h3>Fichier de résultats disponible</h3>
            <p>Téléchargé le: {{ formatDate(resultData.created_at) }}</p>
          </div>
          <div class="result-buttons">
            <button class="view-btn" (click)="viewResultFile()" *ngIf="!showExcelViewer">
              <fa-icon [icon]="faEye" style="margin-right: 10px;"></fa-icon>Afficher
            </button>
            <button class="download-btn" (click)="downloadResultFile()">
              <fa-icon [icon]="faDownload" style="margin-right: 10px;"></fa-icon>Télécharger
            </button>
            <button class="hide-btn" (click)="closeExcelViewer()" *ngIf="showExcelViewer">
              <fa-icon [icon]="faTimes" style="margin-right: 10px;"></fa-icon>Masquer
            </button>
          </div>
        </div>

        <!-- Excel Viewer (Inline) -->
        <div *ngIf="showExcelViewer" class="excel-viewer-inline">
          <div *ngIf="!isLoadingExcel && !excelError" class="excel-content-inline">
            <h3>{{ currentFileName }}</h3>
            <div class="excel-table-container">
              <table class="excel-table" *ngIf="excelData.length > 0">
                <thead>
                  <tr>
                    <th *ngFor="let header of excelHeaders">{{ header }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let row of excelData">
                    <td *ngFor="let cell of row">{{ cell }}</td>
                  </tr>
                </tbody>
              </table>
              <div *ngIf="excelData.length === 0" class="no-data">
                Aucune donnée à afficher
              </div>
            </div>
          </div>
          <div *ngIf="isLoadingExcel" class="loading-spinner">
            <div class="spinner"></div>
            <p>Chargement du fichier Excel...</p>
          </div>
          <div *ngIf="excelError" class="excel-error">
            <p>{{ excelError }}</p>
            <button class="retry-btn" (click)="retryLoadExcel()">
              <fa-icon [icon]="faSync"></fa-icon> Réessayer
            </button>
          </div>
        </div>
      </div>
    </section>

    <div class="action-buttons" [hidden]="hideButtons">
      <button
  *ngIf="reportData  && reportData.status_director !== 'sent'"
  class="send-btn"
  [ngClass]="{'approve-btn': !areFieldsEmpty, 'disabled-btn': areFieldsEmpty}"
  [disabled]="areFieldsEmpty"
  (click)="envoyerRapport()"
>
  <fa-icon [icon]="faPaperPlane" style="margin-right: 10px;"></fa-icon>Envoyer
</button>

      <button class="print-btn" (click)="printRapport()">
        <fa-icon [icon]="faPrint" style="margin-right: 10px;"></fa-icon>Exporter PDF
      </button>
    </div>
  </div>
