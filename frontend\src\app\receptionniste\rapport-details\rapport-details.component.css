/* =======================
   STYLES GLOBAUX
======================= */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Montserrat', sans-serif;
  }
  table {
    border-collapse: collapse;
    width: 100%;
  }

  th, td {
    border: 1px solid black;
    padding: 8px;
    text-align: left;
  }

  /* =======================
     STYLES POUR LES RESULTATS
  ======================= */
  .rapport-results-file {
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .rapport-results-file h2 {
    font-size: 20px;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #2496d3;
  }

  .results-container {
    position: relative;
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #2496d3;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 10px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .no-results {
    padding: 20px;
    text-align: center;
    color: #6c757d;
    font-style: italic;
  }

  .results-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #e9f7fe;
    border-radius: 8px;
    margin-bottom: 15px;
  }

  .result-info h3 {
    font-size: 18px;
    color: #2496d3;
    margin-bottom: 5px;
  }

  .result-info p {
    color: #6c757d;
    font-size: 14px;
  }

  .result-buttons {
    display: flex;
    gap: 10px;
  }

  .view-btn, .download-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
  }

  .view-btn {
    background-color: #2496d3;
    color: white;
  }

  .download-btn {
    background-color: #28a745;
    color: white;
  }

  .view-btn:hover {
    background-color: #1a7bb9;
  }

  .download-btn:hover {
    background-color: #218838;
  }

  /* Excel Viewer Styles (Inline) */
  .excel-viewer-inline {
    margin-top: 20px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    overflow: hidden;
  }

  .excel-content-inline {
    display: flex;
    flex-direction: column;
  }

  .excel-content-inline h3 {
    margin: 0;
    padding: 15px;
    background-color: #f0f0f0;
    color: #333;
    font-size: 16px;
    border-bottom: 1px solid #dee2e6;
  }

  .hide-btn {
    padding: 8px 15px;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
  }

  .hide-btn:hover {
    background-color: #5a6268;
  }

  .excel-table-container {
    overflow: auto;
    max-height: 400px;
    padding: 10px;
  }

  .excel-table {
    width: 100%;
    border-collapse: collapse;
  }

  .excel-table th {
    background-color: #f0f0f0;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  .excel-table th, .excel-table td {
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    text-align: left;
  }

  .excel-table tr:nth-child(even) {
    background-color: #f8f9fa;
  }

  .excel-table tr:hover {
    background-color: #e9f7fe;
  }

  .no-data {
    padding: 20px;
    text-align: center;
    color: #6c757d;
    font-style: italic;
  }

  .excel-error {
    padding: 20px;
    text-align: center;
    background-color: #f8d7da;
    color: #721c24;
    border-radius: 8px;
    margin: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .retry-btn {
    padding: 8px 15px;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
  }
  .status-stamp {
    position: absolute;
    top: 10px; /* Adjust vertical position */
    left: 40px;

     /* Rotate 45 degrees counterclockwise */
    padding: 10px 20px; /* Increase padding for a stamp-like size */
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
    color: white;
     /* Optional border for stamp effect */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); /* Shadow for depth */
    z-index: 1; /* Ensure it stays above other content */
    text-align: center;
    width: 150px; /* Fixed width to control stamp size */
    background-color: rgba(0, 0, 0, 0.2); /* Semi-transparent background */
    transition: transform 0.3s ease-in-out;
  }

  /* Approved Stamp (validation = 1) */
  .approved-stamp {
    background-color: #4caf50; /* Green for Approved */
  }

  /* Rejected Stamp (validation = 0) */
  .rejected-stamp {
    background-color: #f44336; /* Red for Rejected */
  }

  /* Hover effect for interactivity */


  /* Ensure header has relative positioning for absolute child */
  .rapport-header {
    position: relative;
    text-align: center;
    border-bottom: 4px solid #2496d3;
    padding: 30px;
  }

  /* Adjust header-top to accommodate the stamp */
  .header-top {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding-top: 40px; /* Add padding to prevent overlap with the stamp */
  }

  /* Optional: Ensure the stamp doesn't overlap the content below */
  .rapport-container {
    position: relative;
    margin: 40px auto;
    padding: 25px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(36, 150, 211, 0.25);
    animation: fadeIn 0.8s ease-in-out;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  body {
    background-color: #eef7fc; /* Bleu ciel léger */
    color: #333;
    line-height: 1.6;
    font-size: 16px;
  }
  /* =======================
     STYLES POUR LES LOGOS
  ======================= */
  .lab-logo {
    position: absolute;
    left: 20px; /* Position à gauche */
    top: 15px; /* Alignement avec le logo de droite */
    width: 120px; /* Taille du logo */
    height: auto;
    background: white;
    padding: 5px;
    border-radius: 8px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15); /* Ombre douce */
    transition: transform 0.3s ease-in-out;
  }

  .lab-logo:hover {
    transform: scale(1.05); /* Effet léger de zoom au survol */
  }

  /* =======================
     CONTENEUR PRINCIPAL
  ======================= */
  .rapport-container {

    margin: 40px auto;
    padding: 25px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(36, 150, 211, 0.25); /* Ombre améliorée */
    animation: fadeIn 0.8s ease-in-out;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* =======================
     EN-TÊTE DU RAPPORT
  ======================= */
  .rapport-header {
    text-align: center;
    border-bottom: 4px solid #2496d3;
    padding:30px ;
    position: relative;
  }

  .rapport-header .header-top {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .rapport-header h2 {
    font-family: 'Orbitron', sans-serif;
    font-size: 26px;
    font-weight: bold;
    color: #2496d3;
    margin-bottom: 10px;
    text-transform: uppercase;
    animation: slideDown 1s ease-in-out;
  }

  .rapport-header p {
    color: #666;
    font-size: 15px;
    margin-top: 0;
  }

  /* =======================
     LOGO ACCRÉDITATION
  ======================= */
  .accreditation-logo {
    position: absolute;
    top: 15px;  /* Adjust vertical position */
    right: 20px;  /* Adjust horizontal position */
    width: 100px; /* Set proper size */
    height: auto;
    background: white; /* Ensure contrast */
    padding: 10px;
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15); /* Soft shadow */
    transition: transform 0.3s ease-in-out;
  }

  .accreditation-logo:hover {
    transform: scale(1.05); /* Small zoom effect on hover */
  }

  /* =======================
     INFORMATIONS (CLIENT / LABO)
  ======================= */
  .rapport-additional-info {
    display: flex;
    justify-content: space-between;
    gap: 20px;
  }

  .info-left,
  .info-right {
    width: 100%;
    box-shadow: 0 4px 12px rgba(36, 150, 211, 0.15); /* Ombre améliorée */
    padding: 18px;
    border-radius: 10px;
    background-color: #fff;
    transition: transform 0.3s ease-in-out;
  }

  .info-left:hover,
  .info-right:hover {
    transform: translateY(-5px); /* Effet léger de soulèvement */
  }

  .rapport-additional-info h3 {
    color: #2496d3;
    font-size: 20px;
    margin-bottom: 12px;
    font-weight: bold;
  }

  .rapport-additional-info table {
    width: 100%;
    border-collapse: collapse;
  }

  .rapport-additional-info td {
    padding: 10px 0;
    border-bottom: 1px solid #f2f2f2;
  }

  .rapport-additional-info td.label {
    font-weight: 600;
    width: 35%;
    color: #555;
  }

  .rapport-additional-info td.input-field,
  .rapport-additional-info td.lab-info {
    color: #333;
    font-weight: normal;
  }

  /* =======================
     CONTENU (DESCRIPTIONS / ANALYSES / RÉSULTATS)
  ======================= */
  .rapport-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  /* Sections générales */
  .rapport-description,
  .rapport-analyses,
  .rapport-resultats {
    box-shadow: 0 4px 12px rgba(36, 150, 211, 0.15);
    padding: 25px;
    border-radius: 10px;
    background-color: #fff;
    transition: transform 0.3s ease-in-out;
  }

  .rapport-description:hover,
  .rapport-analyses:hover,
  .rapport-resultats:hover {
    transform: translateY(-5px); /* Effet léger */
  }

  .rapport-description h2,
  .rapport-analyses h2,
  .rapport-resultats h2 {
    color: #2496d3;
    font-size: 22px;
    font-weight: 700;
    border-bottom: 3px solid #2496d3;
    padding-bottom: 7px;
    margin-bottom: 18px;
  }

  /* =======================
     BOUTONS D'ACTION
  ======================= */
  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
    margin-bottom: 15px;
  }

  .action-buttons button {
    padding: 12px 24px;
    font-size: 16px;
    background-color: #2496d3;
    color: #fff;
    border-radius: 25px;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-transform: uppercase;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(36, 150, 211, 0.3);
  }

  .action-buttons button:hover {
    background-color: #1e78b5;
    transform: scale(1.05);
  }

  /* Action buttons in table cells */
  .action-buttons-cell {
    display: flex;
    gap: 8px;
    justify-content: center;
  }

  .edit-btn, .update-btn {
    padding: 8px 12px;
    font-size: 13px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    text-transform: uppercase;
    outline: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
  }

  .edit-btn {
    background-color: #555;
    color: white;
    box-shadow: 0 3px 10px rgba(85, 85, 85, 0.3);
  }

  .edit-btn:hover {
    background-color: #444;
    transform: scale(1.05);
  }

  .update-btn {
    background-color: #4caf50;
    color: white;
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
  }

  .update-btn:hover {
    background-color: #3e9d40;
    transform: scale(1.05);
  }

  /* Style for the send button */
  .send-btn {
    background-color: #4caf50;
    color: white;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
  }

  .send-btn:hover {
    background-color: #3e9d40;
    transform: scale(1.05);
  }

  /* Style for the print button */
  .print-btn {
    background-color: #ff9800;
    color: white;
    box-shadow: 0 5px 15px rgba(255, 152, 0, 0.3);
  }

  .print-btn:hover {
    background-color: #e68a00;
    transform: scale(1.05);
  }

  /* Style for the disabled button */
  .disabled-btn {
    background-color: #cccccc !important;
    color: #666666 !important;
    cursor: not-allowed !important;
    box-shadow: none !important;
  }

  .disabled-btn:hover {
    background-color: #cccccc !important;
    transform: none !important;
  }

  .btn-download {
    background-color: #2496d3;
    font-size: 17px;
    font-weight: bold;
  }

  .btn-confirm {
    background-color: #4caf50;
  }

  .btn-confirm:hover {
    background-color: #3e9d40;
  }

  .btn-cancel {
    background-color: #f44336;
  }

  .btn-cancel:hover {
    background-color: #e53935;
  }

  /* =======================
     ANIMATIONS
  ======================= */
  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: translateY(-15px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideDown {
    0% {
      opacity: 0;
      transform: translateY(-30px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* =======================
     RESPONSIVE DESIGN
  ======================= */
  @media (max-width: 768px) {
    .rapport-container {
      width: 90%;
      padding: 15px;
    }

    .accreditation-logo {
      width: 100px;
      top: 10px;
      right: 10px;
    }

    .rapport-additional-info {
      flex-direction: column;
    }

    .modal {
      width: 90%;
      max-width: 400px;
    }
  }
  /* =======================
     TABLE STYLES - Borders and Styling
  ======================= */
  table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    background: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08); /* Light shadow for better visibility */
    border-radius: 8px;
    overflow: hidden;
  }

  /* Default input styles (non-edit mode) */
  input[type="text"],
  input[type="date"] {
    background-color: transparent;
    color: #333;
    border: 1px solid #ddd;
    padding: 8px;
    border-radius: 5px;
    width: 100%;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
  }

  /* Disabled input styles */
  input[type="text"]:disabled,
  input[type="date"]:disabled {
    background-color: #f9f9f9;
    color: #666;
    border: 1px solid #ddd;
    cursor: not-allowed;
  }

  /* Edit mode input styles */
  .editing-row input[type="text"],
  .editing-row input[type="date"] {
    background-color: #e6f0fa; /* ✅ Bleu très clair en fond */
    color: #0d47a1; /* ✅ Bleu foncé pour le texte */
    border: 1px solid #2496d3;
  }

  /* Focus styles for edit mode */
  .editing-row input[type="text"]:focus,
  .editing-row input[type="date"]:focus {
    outline: none;
    border: 1.5px solid #1a6fb5;
    box-shadow: 0 0 6px rgba(36, 150, 211, 0.4);
  }

  /* Borders for headers (th) */
  th {
    background-color: #2496d3; /* Blue header */
    color: white;
    font-weight: bold;
    padding: 12px;
    border: 2px solid #ffffff; /* White borders */
    text-align: center;
  }

  /* Borders for table rows (tr) and table cells (td) */
  tr {
    border-bottom: 1px solid #e0e0e0; /* Light grey border between rows */
    transition: background 0.2s ease-in-out;
  }

  td {
    padding: 12px;
    border: 1px solid #ddd; /* Grey borders for each cell */
    text-align: center;
    color: #333;
  }

  /* Alternate row colors for better readability */
  tr:nth-child(even) {
    background-color: #f9f9f9;
  }

  /* Hover effect for better interaction */
  tr:hover {
    background-color: #eef7fc; /* Light blue on hover */
  }

  /* Adding extra padding for specific sections */
  .rapport-description table,
  .rapport-analyses table,
  .rapport-resultats table {
    margin-bottom: 20px;
    border-radius: 10px;
  }

  /* Nested table styles */
  .nested-table {
    width: 100%;
    margin: 10px 0 0 0;
    box-shadow: none;
    border-radius: 0;
  }

  .nested-table tr {
    border-bottom: 1px solid #e9ecef;
  }

  .nested-table tr:last-child {
    border-bottom: none;
  }

  .nested-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    font-size: 13px;
    padding: 8px;
    text-align: left;
    border: 1px solid #dee2e6;
    width: 40%;
  }

  .nested-table td {
    padding: 8px;
    text-align: left;
    font-size: 13px;
    border: 1px solid #dee2e6;
  }

  /* Responsive design for smaller screens */
  @media (max-width: 768px) {
    th, td {
      padding: 10px;
      font-size: 14px;
    }
  }

  /* Styles for the echantillon description section */
  .echantillon-description-section {
    box-shadow: 0 4px 12px rgba(36, 150, 211, 0.15);
    padding: 25px;
    border-radius: 10px;
    background-color: #fff;
    transition: transform 0.3s ease-in-out;
    margin-bottom: 20px;
  }

  .echantillon-description-section:hover {
    transform: translateY(-5px);
  }

  .echantillon-description-section h2 {
    color: #2496d3;
    font-size: 22px;
    font-weight: 700;
    border-bottom: 3px solid #2496d3;
    padding-bottom: 7px;
    margin-bottom: 18px;
  }

  .echantillon-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    background: white;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
  }

  /* Column width adjustments */
  .code-echantillon-col {
    width: 45%;
  }

  .date-reception-col {
    width: 12%;
  }

  .nature-col {
    width: 12%;
  }

  .provenance-col {
    width: 13%;
  }

  .lot-col, .reference-col {
    width: 9%;
  }

  /* =======================
     REJECTION NOTES SECTION
  ======================= */
  .rejection-notes-section {
    margin: 20px 0;
    padding: 0 20px;
  }

  .rejection-notes-container {
    background-color: #fff5f5;
    border: 2px solid #f56565;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(245, 101, 101, 0.15);
    animation: fadeIn 0.5s ease-in-out;
  }

  .rejection-notes-title {
    color: #e53e3e;
    font-size: 18px;
    font-weight: bold;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .rejection-icon {
    color: #e53e3e;
    font-size: 20px;
  }

  .rejection-notes-content {
    background-color: #ffffff;
    border: 1px solid #fed7d7;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
  }

  .rejection-notes-content p {
    margin: 0;
    color: #2d3748;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  /* Responsive adjustments for rejection notes */
  @media (max-width: 768px) {
    .rejection-notes-section {
      padding: 0 10px;
    }

    .rejection-notes-container {
      padding: 15px;
    }

    .rejection-notes-title {
      font-size: 16px;
    }
  }
